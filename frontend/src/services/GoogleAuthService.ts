import { ApiResponse } from '../types';

export interface GoogleAuthResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    picture?: string;
  };
  token?: string;
  error?: string;
}

export class GoogleAuthService {
  private static baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
  private static googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  /**
   * Initiate Google OAuth flow
   */
  static initiateGoogleAuth(): void {
    if (!this.googleClientId) {
      throw new Error('Google Client ID not configured');
    }

    // Generate a random state parameter for security
    const state = this.generateRandomState();
    sessionStorage.setItem('oauth_state', state);

    // Redirect to backend OAuth endpoint
    const authUrl = `${this.baseUrl}/oauth/google?state=${state}`;
    window.location.href = authUrl;
  }

  /**
   * Handle Google OAuth callback
   */
  static async handleGoogleCallback(code: string, state: string): Promise<GoogleAuthResponse> {
    try {
      // Verify state parameter
      const storedState = sessionStorage.getItem('oauth_state');
      if (state !== storedState) {
        throw new Error('Invalid state parameter');
      }

      // Clear stored state
      sessionStorage.removeItem('oauth_state');

      // Exchange code for tokens via backend
      const response = await fetch(`${this.baseUrl}/oauth/google/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, state }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Google authentication failed');
      }

      // Store token in localStorage
      if (result.token) {
        localStorage.setItem('auth_token', result.token);
      }

      return {
        success: true,
        user: result.user,
        token: result.token,
      };
    } catch (error) {
      console.error('Google OAuth callback error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  /**
   * Get stored auth token
   */
  static getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Sign out user
   */
  static signOut(): void {
    localStorage.removeItem('auth_token');
    sessionStorage.removeItem('oauth_state');
  }

  /**
   * Generate random state parameter for OAuth security
   */
  private static generateRandomState(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get current user info from token
   */
  static async getCurrentUser(): Promise<ApiResponse<any>> {
    try {
      const token = this.getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${this.baseUrl}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to get user info');
      }

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get user info',
      };
    }
  }
}

export default GoogleAuthService;
