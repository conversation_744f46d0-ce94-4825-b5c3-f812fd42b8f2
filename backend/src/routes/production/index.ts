// =============================================================================
// PRODUCTION API ROUTES
// =============================================================================
// Main router for all production API endpoints

import { Router } from 'express';
import authRoutes from './authRoutes';
import vehicleRoutes from './vehicleRoutes';
import bookingRoutes from './bookingRoutes';
import userRoutes from './userRoutes';
import providerRoutes from './providerRoutes';
import paymentRoutes from './paymentRoutes';
import analyticsRoutes from './analyticsRoutes';
import adminRoutes from './adminRoutes';

const router = Router();

// =============================================================================
// API DOCUMENTATION ENDPOINT
// =============================================================================

router.get('/', (req, res) => {
  res.json({
    name: 'RentaHub Production API',
    version: '1.0.0',
    status: 'operational',
    timestamp: new Date().toISOString(),
    documentation: {
      auth: '/api/auth - Authentication endpoints',
      vehicles: '/api/vehicles - Vehicle management',
      bookings: '/api/bookings - Booking management',
      users: '/api/users - User management',
      providers: '/api/providers - Provider management',
      payments: '/api/payments - Payment processing',
      analytics: '/api/analytics - Analytics and reporting',
      admin: '/api/admin - Admin operations'
    },
    endpoints: {
      health: '/health',
      status: '/api/status'
    }
  });
});

// =============================================================================
// ROUTE MOUNTING
// =============================================================================

// Authentication routes
router.use('/auth', authRoutes);

// Vehicle routes
router.use('/vehicles', vehicleRoutes);

// Booking routes
router.use('/bookings', bookingRoutes);

// User routes
router.use('/users', userRoutes);

// Provider routes
router.use('/providers', providerRoutes);

// Payment routes
router.use('/payments', paymentRoutes);

// Analytics routes
router.use('/analytics', analyticsRoutes);

// Admin routes
router.use('/admin', adminRoutes);

export default router;
