# =============================================================================
# RENTAHUB - UNIFIED RAILWAY CONFIGURATION
# =============================================================================
# This configuration deploys the backend service
# Frontend and admin are served as static files from the backend

[build]
builder = "NIXPACKS"
buildCommand = "npm run build:all"

[deploy]
startCommand = "npm start"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[project]
name = "rentahub"

# =============================================================================
# ENVIRONMENT VARIABLES REFERENCE
# =============================================================================
# Set these in Railway dashboard - DO NOT put actual values here
#
# Required variables:
# - NODE_ENV=production
# - DATABASE_URL (Railway PostgreSQL)
# - SUPABASE_URL
# - SUPABASE_ANON_KEY
# - SUPABASE_SERVICE_ROLE_KEY
# - JWT_SECRET
# - STRIPE_SECRET_KEY
# - STRIPE_WEBHOOK_SECRET

# Environment variables that should be shared across all services
[shared.env]
NODE_VERSION = "20"
